
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Alert, Animated, Dimensions, SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAppContext } from '@/context/AppContext';
import { AdvancedAIEngine } from '@/utils/advancedAI';
import { FileInfo, findDuplicateFiles, findOldFiles, formatBytes, getLargeFiles, getMediaFiles, getStorageBreakdown, getStorageInfo } from '@/utils/storageUtils';

const screenWidth = Dimensions.get('window').width;

interface StoragePattern {
  type: 'seasonal' | 'usage-based' | 'app-specific' | 'media-heavy';
  description: string;
  recommendation: string;
  confidence: number;
}

interface PredictiveInsight {
  timeframe: '1week' | '1month' | '3months';
  predictedUsage: number;
  storageFullDate?: Date;
  recommendation: string;
  confidence: number;
}

interface AIInsight {
  id: string;
  type: 'critical' | 'warning' | 'suggestion' | 'success';
  title: string;
  message: string;
  action?: string;
  onPress?: () => void;
  impact: 'high' | 'medium' | 'low';
  savingsEstimate?: string;
}

interface StorageHealthData {
  score: number;
  status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  mainIssue: string;
  recommendation: string;
}

interface SmartCategory {
  name: string;
  size: number;
  fileCount: number;
  icon: string;
  color: string;
  aiDescription: string;
  priority: 'high' | 'medium' | 'low';
}

export default function AIAnalyzeScreen() {
  const { t } = useTranslation();
  const { isDarkMode, isAIEnabled } = useAppContext();
  const router = useRouter();

  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);
  const [storageHealth, setStorageHealth] = useState<StorageHealthData | null>(null);
  const [aiInsights, setAiInsights] = useState<AIInsight[]>([]);
  const [smartCategories, setSmartCategories] = useState<SmartCategory[]>([]);
  const [animatedValue] = useState(new Animated.Value(0));
  const [storageInfo, setStorageInfo] = useState<any>(null);
  const [storagePatterns, setStoragePatterns] = useState<StoragePattern[]>([]);
  const [predictiveInsights, setPredictiveInsights] = useState<PredictiveInsight[]>([]);
  const [storageHolistics, setStorageHolistics] = useState<any>(null);
  const [enhancedInsights, setEnhancedInsights] = useState<any>(null);
  const [aiEngine] = useState(() => AdvancedAIEngine.getInstance());

  const colorScheme = isDarkMode ? 'dark' : 'light';
  const backgroundColor = Colors[colorScheme].background;
  const textColor = Colors[colorScheme].text;
  const accentColor = Colors[colorScheme].tint;
  const cardColor = isDarkMode ? '#1E1E1E' : '#FFFFFF';

  // AI Analysis Engine
  const runAIAnalysis = async () => {
    setIsAnalyzing(true);
    setAnalysisComplete(false);

    try {
      // Get storage data
      const [info, breakdown, largeFiles, media] = await Promise.all([
        getStorageInfo(),
        getStorageBreakdown(),
        getLargeFiles(),
        getMediaFiles()
      ]);

      setStorageInfo(info);

      // Find duplicates and old files
      const duplicates = await findDuplicateFiles(media);
      const oldFiles = findOldFiles(media, 3);

      // Calculate storage health score
      const health = calculateStorageHealth(info, breakdown, largeFiles, Object.values(duplicates).flat(), oldFiles);
      setStorageHealth(health);

      // Generate AI insights
      const insights = generateAIInsights(info, breakdown, largeFiles, Object.values(duplicates).flat(), oldFiles);
      setAiInsights(insights);

      // Create smart categories
      const categories = createSmartCategories(breakdown, largeFiles, Object.values(duplicates).flat());
      setSmartCategories(categories);

      // Advanced AI analysis
      const allFiles = [...largeFiles, ...media, ...Object.values(duplicates).flat()];

      // Analyze storage patterns
      const patterns = await aiEngine.analyzeStoragePatterns([info], allFiles);
      setStoragePatterns(patterns);

      // Generate predictive insights
      const predictions = await aiEngine.predictStorageUsage([info], allFiles);
      setPredictiveInsights(predictions);

      // Generate comprehensive holistics
      const holistics = await aiEngine.generateStorageHolistics(info, allFiles);
      setStorageHolistics(holistics);

      // Generate enhanced insights
      const enhanced = await EnhancedInsightsEngine.generateDetailedInsights(info, allFiles);
      setEnhancedInsights(enhanced);

      // Animate completion
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }).start();

      setAnalysisComplete(true);
    } catch (error) {
      console.error('AI Analysis error:', error);
      Alert.alert(t('analyze.error'), t('analyze.errorMessage'));
    } finally {
      setIsAnalyzing(false);
    }
  };

  const calculateStorageHealth = (info: any, breakdown: any, largeFiles: FileInfo[], duplicates: FileInfo[], oldFiles: FileInfo[]): StorageHealthData => {
    let score = 100;
    let mainIssue = '';
    let recommendation = '';
    let status: StorageHealthData['status'] = 'excellent';

    const usagePercentage = (info.usedSpace / info.totalSpace) * 100;

    // Deduct points for high storage usage
    if (usagePercentage > 90) {
      score -= 40;
      mainIssue = 'Storage almost full';
      recommendation = 'Urgent cleanup needed';
      status = 'critical';
    } else if (usagePercentage > 80) {
      score -= 25;
      mainIssue = 'Storage getting full';
      recommendation = 'Consider cleaning up files';
      status = 'poor';
    } else if (usagePercentage > 70) {
      score -= 15;
      mainIssue = 'Storage moderately full';
      recommendation = 'Regular cleanup recommended';
      status = 'fair';
    }

    // Deduct points for large files
    if (largeFiles.length > 10) {
      score -= 20;
      if (!mainIssue) {
        mainIssue = 'Too many large files';
        recommendation = 'Compress or remove large files';
      }
    } else if (largeFiles.length > 5) {
      score -= 10;
    }

    // Deduct points for duplicates
    if (duplicates.length > 20) {
      score -= 15;
      if (!mainIssue) {
        mainIssue = 'Many duplicate files found';
        recommendation = 'Remove duplicate files';
      }
    } else if (duplicates.length > 10) {
      score -= 8;
    }

    // Deduct points for old files
    if (oldFiles.length > 50) {
      score -= 10;
      if (!mainIssue) {
        mainIssue = 'Many old unused files';
        recommendation = 'Clean up old files';
      }
    }

    score = Math.max(0, score);

    if (score >= 90 && status === 'excellent') {
      mainIssue = 'Storage is well optimized';
      recommendation = 'Keep up the good work!';
    } else if (score >= 70 && status !== 'critical' && status !== 'poor') {
      status = 'good';
    }

    return { score, status, mainIssue, recommendation };
  };

  const generateAIInsights = (info: any, breakdown: any, largeFiles: FileInfo[], duplicates: FileInfo[], oldFiles: FileInfo[]): AIInsight[] => {
    const insights: AIInsight[] = [];
    const usagePercentage = (info.usedSpace / info.totalSpace) * 100;

    // Critical storage warning
    if (usagePercentage > 90) {
      insights.push({
        id: 'critical_storage',
        type: 'critical',
        title: '🚨 Storage Critical',
        message: `Your storage is ${usagePercentage.toFixed(1)}% full. Your phone may slow down or stop working properly.`,
        action: 'Free up space now',
        impact: 'high',
        onPress: () => router.push('/(tabs)/clean')
      });
    }

    // Large files insight
    if (largeFiles.length > 0) {
      const totalSize = largeFiles.reduce((sum, file) => sum + file.size, 0);
      insights.push({
        id: 'large_files',
        type: largeFiles.length > 10 ? 'warning' : 'suggestion',
        title: '📁 Large Files Detected',
        message: `I found ${largeFiles.length} large files taking up ${formatBytes(totalSize)}. These might be videos or photos that can be compressed.`,
        action: 'Compress files',
        impact: 'high',
        savingsEstimate: formatBytes(totalSize * 0.4),
        onPress: () => router.push('/(tabs)/compress')
      });
    }

    // Duplicates insight
    if (duplicates.length > 0) {
      const totalSize = duplicates.reduce((sum, file) => sum + file.size, 0);
      insights.push({
        id: 'duplicates',
        type: 'suggestion',
        title: '🔄 Duplicate Files Found',
        message: `I discovered ${duplicates.length} duplicate files wasting ${formatBytes(totalSize)}. These are exact copies you don't need.`,
        action: 'Remove duplicates',
        impact: 'medium',
        savingsEstimate: formatBytes(totalSize),
        onPress: () => router.push('/(tabs)/clean')
      });
    }

    // Photos taking too much space
    if (breakdown.images > breakdown.videos && breakdown.images > 1024 * 1024 * 1024) {
      insights.push({
        id: 'photo_heavy',
        type: 'suggestion',
        title: '📸 Photo Storage Heavy',
        message: `Your photos are using ${formatBytes(breakdown.images)}. I can help compress them without losing quality.`,
        action: 'Optimize photos',
        impact: 'medium',
        savingsEstimate: formatBytes(breakdown.images * 0.3),
        onPress: () => router.push('/(tabs)/compress')
      });
    }

    // Old files insight
    if (oldFiles.length > 20) {
      const totalSize = oldFiles.reduce((sum, file) => sum + file.size, 0);
      insights.push({
        id: 'old_files',
        type: 'suggestion',
        title: '🕒 Old Files Cleanup',
        message: `I found ${oldFiles.length} files older than 3 months that you might not need anymore.`,
        action: 'Review old files',
        impact: 'low',
        savingsEstimate: formatBytes(totalSize),
        onPress: () => router.push('/(tabs)/clean')
      });
    }

    // Success message if storage is good
    if (usagePercentage < 50 && largeFiles.length < 5 && duplicates.length < 5) {
      insights.push({
        id: 'storage_healthy',
        type: 'success',
        title: '✨ Storage Looks Great!',
        message: 'Your storage is well organized and optimized. I\'ll keep monitoring for any issues.',
        impact: 'low'
      });
    }

    return insights;
  };

  const createSmartCategories = (breakdown: any, largeFiles: FileInfo[], duplicates: FileInfo[]): SmartCategory[] => {
    const categories: SmartCategory[] = [];

    // Images category
    if (breakdown.images > 0) {
      categories.push({
        name: 'Photos & Images',
        size: breakdown.images,
        fileCount: 0, // We'll estimate this
        icon: 'photo.fill',
        color: '#FF6B6B',
        aiDescription: breakdown.images > 1024 * 1024 * 1024 ? 
          'Taking up significant space. Consider compression.' : 
          'Well managed photo collection.',
        priority: breakdown.images > 1024 * 1024 * 1024 ? 'high' : 'low'
      });
    }

    // Videos category
    if (breakdown.videos > 0) {
      categories.push({
        name: 'Videos',
        size: breakdown.videos,
        fileCount: 0,
        icon: 'video.fill',
        color: '#4ECDC4',
        aiDescription: breakdown.videos > breakdown.images ? 
          'Videos are your largest storage consumer.' : 
          'Video storage is under control.',
        priority: breakdown.videos > 2 * 1024 * 1024 * 1024 ? 'high' : 'medium'
      });
    }

    // Apps category
    if (breakdown.apps > 0) {
      categories.push({
        name: 'Apps & Games',
        size: breakdown.apps,
        fileCount: 0,
        icon: 'app.fill',
        color: '#45B7D1',
        aiDescription: 'App data and cache files.',
        priority: 'medium'
      });
    }

    // Documents category
    if (breakdown.documents > 0) {
      categories.push({
        name: 'Documents',
        size: breakdown.documents,
        fileCount: 0,
        icon: 'doc.fill',
        color: '#96CEB4',
        aiDescription: 'Documents and files.',
        priority: 'low'
      });
    }

    return categories.sort((a, b) => b.size - a.size);
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'excellent': return '#4CAF50';
      case 'good': return '#8BC34A';
      case 'fair': return '#FF9800';
      case 'poor': return '#FF5722';
      case 'critical': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'critical': return 'exclamationmark.triangle.fill';
      case 'warning': return 'exclamationmark.circle.fill';
      case 'suggestion': return 'lightbulb.fill';
      case 'success': return 'checkmark.circle.fill';
      default: return 'info.circle.fill';
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'critical': return '#F44336';
      case 'warning': return '#FF9800';
      case 'suggestion': return '#2196F3';
      case 'success': return '#4CAF50';
      default: return '#9E9E9E';
    }
  };

  const getPatternColor = (type: string) => {
    switch (type) {
      case 'seasonal': return '#FF6B6B';
      case 'usage-based': return '#4ECDC4';
      case 'app-specific': return '#45B7D1';
      case 'media-heavy': return '#96CEB4';
      default: return '#9E9E9E';
    }
  };

  const getPatternIcon = (type: string) => {
    switch (type) {
      case 'seasonal': return 'calendar';
      case 'usage-based': return 'chart.bar.fill';
      case 'app-specific': return 'app.fill';
      case 'media-heavy': return 'photo.fill';
      default: return 'info.circle.fill';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return '#4CAF50';
    if (confidence >= 60) return '#FF9800';
    return '#FF5722';
  };

  const getUsageIntensityColor = (intensity: string) => {
    switch (intensity) {
      case 'light': return '#4CAF50';
      case 'moderate': return '#FF9800';
      case 'heavy': return '#FF5722';
      case 'extreme': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getSizeRangeLabel = (range: string) => {
    switch (range) {
      case 'tiny': return 'Tiny (< 1MB)';
      case 'small': return 'Small (1-10MB)';
      case 'medium': return 'Medium (10-100MB)';
      case 'large': return 'Large (100MB-1GB)';
      case 'huge': return 'Huge (> 1GB)';
      default: return range;
    }
  };

  const getEffortColor = (effort: string) => {
    switch (effort) {
      case 'low': return '#4CAF50';
      case 'medium': return '#FF9800';
      case 'high': return '#FF5722';
      default: return '#9E9E9E';
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return '#4CAF50';
    if (score >= 60) return '#FF9800';
    if (score >= 40) return '#FF5722';
    return '#F44336';
  };

  const getMetricColor = (value: number) => {
    if (value <= 20) return '#4CAF50';
    if (value <= 50) return '#FF9800';
    return '#FF5722';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return '#F44336';
      case 'warning': return '#FF9800';
      case 'info': return '#2196F3';
      default: return '#9E9E9E';
    }
  };

  useEffect(() => {
    if (isAIEnabled) {
      runAIAnalysis();
    }
  }, [isAIEnabled]);

  if (!isAIEnabled) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.disabledContainer}>
          <IconSymbol name="brain" size={64} color={isDarkMode ? '#555' : '#CCC'} />
          <Text style={[styles.disabledTitle, { color: textColor }]}>
            {t('analyze.aiDisabled')}
          </Text>
          <Text style={[styles.disabledMessage, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
            {t('analyze.enableAI')}
          </Text>
          <TouchableOpacity
            style={[styles.enableButton, { backgroundColor: accentColor }]}
            onPress={() => router.push('/(tabs)/settings')}
          >
            <Text style={styles.enableButtonText}>{t('analyze.goToSettings')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <IconSymbol name="brain" size={28} color={accentColor} />
          <Text style={[styles.title, { color: textColor }]}>{t('analyze.aiAnalysisTitle')}</Text>
        </View>
        {!isAnalyzing && analysisComplete && (
          <TouchableOpacity
            style={[styles.refreshButton, { backgroundColor: isDarkMode ? '#333' : '#F0F0F0' }]}
            onPress={runAIAnalysis}
          >
            <IconSymbol name="arrow.clockwise" size={20} color={textColor} />
          </TouchableOpacity>
        )}
      </View>

      {isAnalyzing ? (
        <View style={styles.analyzingContainer}>
          <ActivityIndicator size="large" color={accentColor} />
          <Text style={[styles.analyzingText, { color: textColor }]}>
            🧠 {t('analyze.aiAnalyzing')}
          </Text>
          <Text style={[styles.analyzingSubtext, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
            {t('analyze.aiAnalyzingSubtext')}
          </Text>
        </View>
      ) : analysisComplete && storageHealth ? (
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Storage Health Score */}
          <Animated.View style={[
            styles.healthCard,
            { backgroundColor: cardColor, opacity: animatedValue }
          ]}>
            <View
              style={[styles.healthGradient, { backgroundColor: getHealthColor(storageHealth.status) }]}
            >
              <View style={styles.healthHeader}>
                <Text style={styles.healthScore}>{storageHealth.score}</Text>
                <Text style={styles.healthScoreLabel}>{t('analyze.storageHealth')}</Text>
              </View>
              <View style={styles.healthInfo}>
                <Text style={styles.healthStatus}>{storageHealth.status.toUpperCase()}</Text>
                <Text style={styles.healthIssue}>{storageHealth.mainIssue}</Text>
                <Text style={styles.healthRecommendation}>{storageHealth.recommendation}</Text>
              </View>
            </View>
          </Animated.View>

          {/* AI Insights */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: textColor }]}>
              🤖 {t('analyze.aiInsights')}
            </Text>
            {aiInsights.map((insight, index) => (
              <Animated.View
                key={insight.id}
                style={[
                  styles.insightCard,
                  { backgroundColor: cardColor, opacity: animatedValue },
                  { transform: [{ translateY: animatedValue.interpolate({
                    inputRange: [0, 1],
                    outputRange: [50, 0]
                  })}]}
                ]}
              >
                <TouchableOpacity
                  style={styles.insightContent}
                  onPress={insight.onPress}
                  disabled={!insight.onPress}
                >
                  <View style={styles.insightHeader}>
                    <View style={[styles.insightIcon, { backgroundColor: getInsightColor(insight.type) }]}>
                      <IconSymbol name={getInsightIcon(insight.type)} size={16} color="#FFFFFF" />
                    </View>
                    <View style={styles.insightTextContainer}>
                      <Text style={[styles.insightTitle, { color: textColor }]}>
                        {insight.title}
                      </Text>
                      <Text style={[styles.insightMessage, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                        {insight.message}
                      </Text>
                    </View>
                  </View>
                  {insight.action && (
                    <View style={styles.insightFooter}>
                      {insight.savingsEstimate && (
                        <Text style={[styles.savingsEstimate, { color: '#4CAF50' }]}>
                          Save ~{insight.savingsEstimate}
                        </Text>
                      )}
                      <Text style={[styles.insightAction, { color: accentColor }]}>
                        {insight.action} →
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              </Animated.View>
            ))}
          </View>

          {/* Storage Overview Holistics */}
          {storageHolistics && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: textColor }]}>
                📈 Storage Overview
              </Text>

              {/* Key Metrics */}
              <Animated.View style={[styles.holisticsCard, { backgroundColor: cardColor, opacity: animatedValue }]}>
                <View style={styles.metricsGrid}>
                  <View style={styles.metricItem}>
                    <Text style={[styles.metricValue, { color: accentColor }]}>
                      {storageHolistics.overview.totalFiles.toLocaleString()}
                    </Text>
                    <Text style={[styles.metricLabel, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                      Total Files
                    </Text>
                  </View>
                  <View style={styles.metricItem}>
                    <Text style={[styles.metricValue, { color: accentColor }]}>
                      {formatBytes(storageHolistics.overview.averageFileSize)}
                    </Text>
                    <Text style={[styles.metricLabel, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                      Avg Size
                    </Text>
                  </View>
                  <View style={styles.metricItem}>
                    <Text style={[styles.metricValue, { color: getUsageIntensityColor(storageHolistics.trends.usageIntensity) }]}>
                      {storageHolistics.trends.usageIntensity.toUpperCase()}
                    </Text>
                    <Text style={[styles.metricLabel, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                      Usage
                    </Text>
                  </View>
                  <View style={styles.metricItem}>
                    <Text style={[styles.metricValue, { color: formatBytes(storageHolistics.optimization.potentialSavings) !== '0 B' ? '#4CAF50' : '#9E9E9E' }]}>
                      {formatBytes(storageHolistics.optimization.potentialSavings)}
                    </Text>
                    <Text style={[styles.metricLabel, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                      Potential Savings
                    </Text>
                  </View>
                </View>
              </Animated.View>

              {/* Quick Wins */}
              {storageHolistics.optimization.quickWins.length > 0 && (
                <Animated.View style={[styles.quickWinsCard, { backgroundColor: cardColor, opacity: animatedValue }]}>
                  <Text style={[styles.cardTitle, { color: textColor }]}>⚡ Quick Wins</Text>
                  {storageHolistics.optimization.quickWins.slice(0, 3).map((win: any, index: number) => (
                    <View key={index} style={styles.quickWinItem}>
                      <View style={[styles.effortBadge, { backgroundColor: getEffortColor(win.effort) }]}>
                        <Text style={styles.effortText}>{win.effort.toUpperCase()}</Text>
                      </View>
                      <View style={styles.quickWinInfo}>
                        <Text style={[styles.quickWinAction, { color: textColor }]}>
                          {win.action}
                        </Text>
                        <Text style={[styles.quickWinSavings, { color: '#4CAF50' }]}>
                          Save {formatBytes(win.savings)}
                        </Text>
                      </View>
                    </View>
                  ))}
                </Animated.View>
              )}
            </View>
          )}

          {/* Enhanced Insights */}
          {enhancedInsights && enhancedInsights.insights.length > 0 && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: textColor }]}>
                🔍 Detailed Analysis
              </Text>

              {/* Health Score */}
              <Animated.View style={[styles.healthScoreCard, { backgroundColor: cardColor, opacity: animatedValue }]}>
                <View style={styles.healthScoreHeader}>
                  <Text style={[styles.healthScoreTitle, { color: textColor }]}>Storage Health Score</Text>
                  <Text style={[styles.healthScoreValue, { color: getHealthScoreColor(enhancedInsights.healthMetrics.overallScore) }]}>
                    {enhancedInsights.healthMetrics.overallScore}/100
                  </Text>
                </View>
                <View style={styles.healthMetricsGrid}>
                  <View style={styles.healthMetricItem}>
                    <Text style={[styles.healthMetricLabel, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                      Fragmentation
                    </Text>
                    <Text style={[styles.healthMetricValue, { color: getMetricColor(enhancedInsights.healthMetrics.fragmentationLevel) }]}>
                      {enhancedInsights.healthMetrics.fragmentationLevel}%
                    </Text>
                  </View>
                  <View style={styles.healthMetricItem}>
                    <Text style={[styles.healthMetricLabel, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                      Redundancy
                    </Text>
                    <Text style={[styles.healthMetricValue, { color: getMetricColor(enhancedInsights.healthMetrics.redundancyRatio) }]}>
                      {enhancedInsights.healthMetrics.redundancyRatio}%
                    </Text>
                  </View>
                  <View style={styles.healthMetricItem}>
                    <Text style={[styles.healthMetricLabel, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                      Compression
                    </Text>
                    <Text style={[styles.healthMetricValue, { color: '#4CAF50' }]}>
                      {enhancedInsights.healthMetrics.compressionOpportunity}%
                    </Text>
                  </View>
                </View>
              </Animated.View>

              {/* Detailed Insights */}
              {enhancedInsights.insights.slice(0, 5).map((insight: any, index: number) => (
                <Animated.View
                  key={insight.id}
                  style={[
                    styles.detailedInsightCard,
                    { backgroundColor: cardColor, opacity: animatedValue }
                  ]}
                >
                  <View style={styles.detailedInsightHeader}>
                    <View style={[styles.severityBadge, { backgroundColor: getSeverityColor(insight.severity) }]}>
                      <Text style={styles.severityText}>{insight.severity.toUpperCase()}</Text>
                    </View>
                    <View style={styles.detailedInsightTitleContainer}>
                      <Text style={[styles.detailedInsightTitle, { color: textColor }]}>
                        {insight.title}
                      </Text>
                      <Text style={[styles.detailedInsightCategory, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                        {insight.category.toUpperCase()}
                      </Text>
                    </View>
                    {insight.confidence && (
                      <View style={[styles.confidenceBadge, { backgroundColor: getConfidenceColor(insight.confidence) }]}>
                        <Text style={styles.confidenceText}>{insight.confidence}%</Text>
                      </View>
                    )}
                  </View>

                  <Text style={[styles.detailedInsightDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                    {insight.description}
                  </Text>

                  <Text style={[styles.detailedInsightImpact, { color: textColor }]}>
                    💥 Impact: {insight.impact}
                  </Text>

                  <Text style={[styles.detailedInsightRecommendation, { color: accentColor }]}>
                    💡 {insight.recommendation}
                  </Text>

                  {insight.estimatedSavings && (
                    <Text style={[styles.detailedInsightSavings, { color: '#4CAF50' }]}>
                      💾 Potential savings: {formatBytes(insight.estimatedSavings)}
                    </Text>
                  )}
                </Animated.View>
              ))}

              {/* Action Recommendations */}
              {enhancedInsights.recommendations.length > 0 && (
                <Animated.View style={[styles.recommendationsCard, { backgroundColor: cardColor, opacity: animatedValue }]}>
                  <Text style={[styles.cardTitle, { color: textColor }]}>🎯 Action Plan</Text>
                  {enhancedInsights.recommendations.map((rec: string, index: number) => (
                    <Text key={index} style={[styles.recommendationItem, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                      {rec}
                    </Text>
                  ))}
                </Animated.View>
              )}
            </View>
          )}

          {/* Storage Patterns */}
          {storagePatterns.length > 0 && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: textColor }]}>
                🧠 AI Storage Patterns
              </Text>
              {storagePatterns.map((pattern, index) => (
                <Animated.View
                  key={index}
                  style={[
                    styles.patternCard,
                    { backgroundColor: cardColor, opacity: animatedValue }
                  ]}
                >
                  <View style={styles.patternHeader}>
                    <View style={[styles.patternIcon, { backgroundColor: getPatternColor(pattern.type) }]}>
                      <IconSymbol name={getPatternIcon(pattern.type)} size={16} color="#FFFFFF" />
                    </View>
                    <View style={styles.patternInfo}>
                      <Text style={[styles.patternType, { color: textColor }]}>
                        {pattern.type.toUpperCase()} PATTERN
                      </Text>
                      <Text style={[styles.patternDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                        {pattern.description}
                      </Text>
                    </View>
                    <View style={[styles.confidenceBadge, { backgroundColor: getConfidenceColor(pattern.confidence) }]}>
                      <Text style={styles.confidenceText}>{pattern.confidence}%</Text>
                    </View>
                  </View>
                  <Text style={[styles.patternRecommendation, { color: accentColor }]}>
                    💡 {pattern.recommendation}
                  </Text>
                </Animated.View>
              ))}
            </View>
          )}

          {/* Predictive Insights */}
          {predictiveInsights.length > 0 && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: textColor }]}>
                🔮 Predictive Analysis
              </Text>
              {predictiveInsights.map((insight, index) => (
                <Animated.View
                  key={insight.timeframe}
                  style={[
                    styles.predictionCard,
                    { backgroundColor: cardColor, opacity: animatedValue }
                  ]}
                >
                  <View style={styles.predictionHeader}>
                    <Text style={[styles.predictionTimeframe, { color: textColor }]}>
                      {insight.timeframe.toUpperCase()}
                    </Text>
                    <Text style={[styles.predictionUsage, { color: accentColor }]}>
                      {formatBytes(insight.predictedUsage)}
                    </Text>
                  </View>
                  {insight.storageFullDate && (
                    <Text style={[styles.predictionWarning, { color: '#FF5722' }]}>
                      ⚠️ Storage may be full by {insight.storageFullDate.toLocaleDateString()}
                    </Text>
                  )}
                  <Text style={[styles.predictionRecommendation, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                    {insight.recommendation}
                  </Text>
                  <View style={[styles.confidenceBadge, { backgroundColor: getConfidenceColor(insight.confidence) }]}>
                    <Text style={styles.confidenceText}>{insight.confidence}% confidence</Text>
                  </View>
                </Animated.View>
              ))}
            </View>
          )}

          {/* Smart Categories */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: textColor }]}>
              📊 {t('analyze.smartCategories')}
            </Text>
            {smartCategories.map((category, index) => (
              <Animated.View
                key={category.name}
                style={[
                  styles.categoryCard,
                  { backgroundColor: cardColor, opacity: animatedValue }
                ]}
              >
                <View style={styles.categoryHeader}>
                  <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                    <IconSymbol name={category.icon} size={20} color="#FFFFFF" />
                  </View>
                  <View style={styles.categoryInfo}>
                    <Text style={[styles.categoryName, { color: textColor }]}>
                      {category.name}
                    </Text>
                    <Text style={[styles.categorySize, { color: category.color }]}>
                      {formatBytes(category.size)}
                    </Text>
                  </View>
                  <View style={[
                    styles.priorityBadge,
                    { backgroundColor: category.priority === 'high' ? '#FF5722' :
                                     category.priority === 'medium' ? '#FF9800' : '#4CAF50' }
                  ]}>
                    <Text style={styles.priorityText}>
                      {category.priority.toUpperCase()}
                    </Text>
                  </View>
                </View>
                <Text style={[styles.categoryDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                  {category.aiDescription}
                </Text>
              </Animated.View>
            ))}
          </View>
        </ScrollView>
      ) : (
        <View style={styles.startContainer}>
          <IconSymbol name="brain" size={64} color={accentColor} />
          <Text style={[styles.startTitle, { color: textColor }]}>
            {t('analyze.readyForAnalysis')}
          </Text>
          <Text style={[styles.startMessage, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
            {t('analyze.readyForAnalysisMessage')}
          </Text>
          <TouchableOpacity
            style={[styles.startButton, { backgroundColor: accentColor }]}
            onPress={runAIAnalysis}
          >
            <Text style={styles.startButtonText}>{t('analyze.startAIAnalysis')}</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingTop: 10,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginLeft: 12,
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  disabledTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  disabledMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  enableButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  enableButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  analyzingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  analyzingText: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 24,
    marginBottom: 8,
  },
  analyzingSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  startContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  startTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  startMessage: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
  },
  startButton: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  healthCard: {
    borderRadius: 16,
    marginBottom: 24,
    overflow: 'hidden',
  },
  healthGradient: {
    padding: 24,
  },
  healthHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  healthScore: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  healthScoreLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
  },
  healthInfo: {
    alignItems: 'center',
  },
  healthStatus: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  healthIssue: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 4,
    textAlign: 'center',
  },
  healthRecommendation: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  insightCard: {
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
  },
  insightContent: {
    padding: 16,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  insightIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  insightTextContainer: {
    flex: 1,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  insightMessage: {
    fontSize: 14,
    lineHeight: 20,
  },
  insightFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  savingsEstimate: {
    fontSize: 12,
    fontWeight: '600',
  },
  insightAction: {
    fontSize: 14,
    fontWeight: '600',
  },
  categoryCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  categorySize: {
    fontSize: 14,
    fontWeight: '500',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  categoryDescription: {
    fontSize: 13,
    fontStyle: 'italic',
  },
  // Pattern card styles
  patternCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  patternHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  patternIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  patternInfo: {
    flex: 1,
  },
  patternType: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  patternDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  patternRecommendation: {
    fontSize: 13,
    fontWeight: '500',
    marginTop: 8,
  },
  confidenceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  confidenceText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  // Prediction card styles
  predictionCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  predictionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  predictionTimeframe: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  predictionUsage: {
    fontSize: 16,
    fontWeight: '600',
  },
  predictionWarning: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 8,
  },
  predictionRecommendation: {
    fontSize: 13,
    marginBottom: 8,
  },
  // Holistics card styles
  holisticsCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  distributionCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  distributionSection: {
    marginBottom: 16,
  },
  distributionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  distributionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  distributionInfo: {
    flex: 1,
  },
  distributionType: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  distributionCount: {
    fontSize: 12,
  },
  distributionRight: {
    alignItems: 'flex-end',
  },
  distributionSize: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  distributionPercentage: {
    fontSize: 12,
  },
  quickWinsCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  quickWinItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  effortBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginRight: 12,
  },
  effortText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  quickWinInfo: {
    flex: 1,
  },
  quickWinAction: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  quickWinSavings: {
    fontSize: 12,
    fontWeight: '600',
  },
  // Enhanced insights styles
  healthScoreCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  healthScoreHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  healthScoreTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  healthScoreValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  healthMetricsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  healthMetricItem: {
    alignItems: 'center',
  },
  healthMetricLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  healthMetricValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  detailedInsightCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  detailedInsightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  severityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginRight: 12,
  },
  severityText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  detailedInsightTitleContainer: {
    flex: 1,
  },
  detailedInsightTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  detailedInsightCategory: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  detailedInsightDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  detailedInsightImpact: {
    fontSize: 13,
    marginBottom: 8,
    fontStyle: 'italic',
  },
  detailedInsightRecommendation: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  detailedInsightSavings: {
    fontSize: 13,
    fontWeight: '600',
  },
  recommendationsCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  recommendationItem: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
});
