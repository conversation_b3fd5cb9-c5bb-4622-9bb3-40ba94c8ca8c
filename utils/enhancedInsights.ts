import { FileInfo, StorageInfo, formatBytes } from './storageUtils';

export interface DetailedInsight {
  id: string;
  category: 'storage' | 'performance' | 'security' | 'optimization' | 'behavior';
  severity: 'info' | 'warning' | 'critical';
  title: string;
  description: string;
  impact: string;
  recommendation: string;
  actionable: boolean;
  estimatedSavings?: number;
  timeToImplement: 'immediate' | 'short' | 'medium' | 'long';
  confidence: number;
}

export interface StorageHealthMetrics {
  overallScore: number;
  fragmentationLevel: number;
  redundancyRatio: number;
  accessPatternEfficiency: number;
  compressionOpportunity: number;
  cleanupPotential: number;
}

/**
 * Enhanced AI insights generator with deep analysis
 */
export class EnhancedInsightsEngine {
  /**
   * Generate comprehensive storage insights
   */
  static async generateDetailedInsights(
    storageInfo: StorageInfo,
    files: FileInfo[]
  ): Promise<{
    insights: DetailedInsight[];
    healthMetrics: StorageHealthMetrics;
    recommendations: string[];
  }> {
    const insights: DetailedInsight[] = [];
    const usagePercentage = (storageInfo.usedSpace / storageInfo.totalSpace) * 100;

    // Storage capacity insights
    if (usagePercentage > 95) {
      insights.push({
        id: 'critical_storage_full',
        category: 'storage',
        severity: 'critical',
        title: 'Storage Critically Full',
        description: `Your device storage is ${usagePercentage.toFixed(1)}% full. This can severely impact device performance and prevent app updates.`,
        impact: 'Device may become unresponsive, apps may crash, and system updates will fail.',
        recommendation: 'Immediately free up at least 2GB of space by deleting unnecessary files or moving them to cloud storage.',
        actionable: true,
        timeToImplement: 'immediate',
        confidence: 98
      });
    } else if (usagePercentage > 85) {
      insights.push({
        id: 'high_storage_usage',
        category: 'storage',
        severity: 'warning',
        title: 'High Storage Usage Detected',
        description: `Storage is ${usagePercentage.toFixed(1)}% full. Performance degradation may begin soon.`,
        impact: 'Slower app launches, reduced camera functionality, and limited download capability.',
        recommendation: 'Plan to free up 1-2GB of space within the next week.',
        actionable: true,
        timeToImplement: 'short',
        confidence: 90
      });
    }

    // File fragmentation analysis
    const fragmentationLevel = this.calculateFragmentation(files);
    if (fragmentationLevel > 0.7) {
      insights.push({
        id: 'high_fragmentation',
        category: 'performance',
        severity: 'warning',
        title: 'Storage Fragmentation Detected',
        description: 'Your files are highly fragmented, which can slow down file access and app performance.',
        impact: 'Slower file operations, reduced battery life, and degraded user experience.',
        recommendation: 'Consider reorganizing files or using built-in storage optimization tools.',
        actionable: true,
        timeToImplement: 'medium',
        confidence: 75
      });
    }

    // Duplicate file analysis
    const duplicateAnalysis = this.analyzeDuplicates(files);
    if (duplicateAnalysis.count > 10) {
      const savings = duplicateAnalysis.totalSize * 0.8; // Keep one copy
      insights.push({
        id: 'duplicate_files',
        category: 'optimization',
        severity: duplicateAnalysis.count > 50 ? 'warning' : 'info',
        title: `${duplicateAnalysis.count} Duplicate Files Found`,
        description: `Found ${duplicateAnalysis.count} duplicate files wasting ${formatBytes(duplicateAnalysis.totalSize)}.`,
        impact: 'Unnecessary storage consumption and potential confusion when accessing files.',
        recommendation: 'Remove duplicate files to free up space and improve organization.',
        actionable: true,
        estimatedSavings: savings,
        timeToImplement: 'short',
        confidence: 85
      });
    }

    // Large file analysis
    const largeFiles = files.filter(f => f.size > 100 * 1024 * 1024); // > 100MB
    if (largeFiles.length > 5) {
      const totalLargeSize = largeFiles.reduce((sum, f) => sum + f.size, 0);
      insights.push({
        id: 'large_files',
        category: 'optimization',
        severity: 'info',
        title: `${largeFiles.length} Large Files Detected`,
        description: `Found ${largeFiles.length} files larger than 100MB, totaling ${formatBytes(totalLargeSize)}.`,
        impact: 'Large files consume significant storage and may slow down backups.',
        recommendation: 'Review large files and consider compression or cloud storage for rarely accessed items.',
        actionable: true,
        estimatedSavings: totalLargeSize * 0.4,
        timeToImplement: 'medium',
        confidence: 80
      });
    }

    // Old file analysis
    const oldFiles = this.findOldFiles(files, 365); // 1 year
    if (oldFiles.length > 20) {
      const oldSize = oldFiles.reduce((sum, f) => sum + f.size, 0);
      insights.push({
        id: 'old_files',
        category: 'optimization',
        severity: 'info',
        title: `${oldFiles.length} Old Files Found`,
        description: `Found ${oldFiles.length} files older than 1 year, totaling ${formatBytes(oldSize)}.`,
        impact: 'Old files may no longer be needed and consume valuable storage space.',
        recommendation: 'Archive or delete files that haven\'t been accessed in over a year.',
        actionable: true,
        estimatedSavings: oldSize * 0.7,
        timeToImplement: 'long',
        confidence: 70
      });
    }

    // Media compression opportunities
    const mediaAnalysis = this.analyzeMediaFiles(files);
    if (mediaAnalysis.compressibleSize > 500 * 1024 * 1024) { // > 500MB
      insights.push({
        id: 'media_compression',
        category: 'optimization',
        severity: 'info',
        title: 'Media Compression Opportunity',
        description: `${formatBytes(mediaAnalysis.compressibleSize)} of media files can be compressed without quality loss.`,
        impact: 'Significant storage savings while maintaining visual quality.',
        recommendation: 'Use AI-powered compression to reduce file sizes by 30-60% without visible quality loss.',
        actionable: true,
        estimatedSavings: mediaAnalysis.compressibleSize * 0.45,
        timeToImplement: 'short',
        confidence: 88
      });
    }

    // Security insights
    const securityAnalysis = this.analyzeSecurityRisks(files);
    if (securityAnalysis.riskyFiles > 0) {
      insights.push({
        id: 'security_risk',
        category: 'security',
        severity: 'warning',
        title: 'Potential Security Risks Detected',
        description: `Found ${securityAnalysis.riskyFiles} files that may pose security risks.`,
        impact: 'Potential privacy breaches or malware infections.',
        recommendation: 'Review and remove suspicious files, especially those from unknown sources.',
        actionable: true,
        timeToImplement: 'immediate',
        confidence: 65
      });
    }

    // Calculate health metrics
    const healthMetrics = this.calculateHealthMetrics(storageInfo, files, insights);

    // Generate actionable recommendations
    const recommendations = this.generateActionableRecommendations(insights, healthMetrics);

    return {
      insights: insights.sort((a, b) => {
        const severityOrder = { critical: 3, warning: 2, info: 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      }),
      healthMetrics,
      recommendations
    };
  }

  private static calculateFragmentation(files: FileInfo[]): number {
    // Simple fragmentation calculation based on file size distribution
    const sizeVariance = this.calculateSizeVariance(files);
    const avgSize = files.reduce((sum, f) => sum + f.size, 0) / files.length;
    return Math.min(1, sizeVariance / (avgSize * avgSize));
  }

  private static calculateSizeVariance(files: FileInfo[]): number {
    const avgSize = files.reduce((sum, f) => sum + f.size, 0) / files.length;
    const variance = files.reduce((sum, f) => sum + Math.pow(f.size - avgSize, 2), 0) / files.length;
    return variance;
  }

  private static analyzeDuplicates(files: FileInfo[]): { count: number; totalSize: number } {
    const sizeGroups: { [size: number]: FileInfo[] } = {};
    
    files.forEach(file => {
      if (file.size > 0) {
        if (!sizeGroups[file.size]) {
          sizeGroups[file.size] = [];
        }
        sizeGroups[file.size].push(file);
      }
    });

    let duplicateCount = 0;
    let totalDuplicateSize = 0;

    Object.values(sizeGroups).forEach(group => {
      if (group.length > 1) {
        duplicateCount += group.length - 1; // Count extras as duplicates
        totalDuplicateSize += group[0].size * (group.length - 1);
      }
    });

    return { count: duplicateCount, totalSize: totalDuplicateSize };
  }

  private static findOldFiles(files: FileInfo[], daysOld: number): FileInfo[] {
    const cutoffDate = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
    return files.filter(f => f.modificationTime && f.modificationTime < cutoffDate);
  }

  private static analyzeMediaFiles(files: FileInfo[]): { compressibleSize: number; count: number } {
    const mediaFiles = files.filter(f => f.type === 'image' || f.type === 'video');
    const compressibleFiles = mediaFiles.filter(f => f.size > 1024 * 1024); // > 1MB
    
    return {
      compressibleSize: compressibleFiles.reduce((sum, f) => sum + f.size, 0),
      count: compressibleFiles.length
    };
  }

  private static analyzeSecurityRisks(files: FileInfo[]): { riskyFiles: number } {
    const riskyExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];
    const riskyFiles = files.filter(f => 
      riskyExtensions.some(ext => f.name.toLowerCase().endsWith(ext)) ||
      f.name.toLowerCase().includes('crack') ||
      f.name.toLowerCase().includes('keygen')
    );

    return { riskyFiles: riskyFiles.length };
  }

  private static calculateHealthMetrics(
    storageInfo: StorageInfo,
    files: FileInfo[],
    insights: DetailedInsight[]
  ): StorageHealthMetrics {
    const usagePercentage = (storageInfo.usedSpace / storageInfo.totalSpace) * 100;
    
    // Overall score (0-100)
    let overallScore = 100;
    overallScore -= Math.max(0, (usagePercentage - 70) * 2); // Penalty for high usage
    overallScore -= insights.filter(i => i.severity === 'critical').length * 20;
    overallScore -= insights.filter(i => i.severity === 'warning').length * 10;
    overallScore = Math.max(0, Math.min(100, overallScore));

    const fragmentationLevel = this.calculateFragmentation(files);
    const duplicateAnalysis = this.analyzeDuplicates(files);
    const redundancyRatio = duplicateAnalysis.totalSize / storageInfo.usedSpace;
    
    const mediaAnalysis = this.analyzeMediaFiles(files);
    const compressionOpportunity = mediaAnalysis.compressibleSize / storageInfo.usedSpace;
    
    const oldFiles = this.findOldFiles(files, 180); // 6 months
    const cleanupPotential = oldFiles.reduce((sum, f) => sum + f.size, 0) / storageInfo.usedSpace;

    return {
      overallScore: Math.round(overallScore),
      fragmentationLevel: Math.round(fragmentationLevel * 100),
      redundancyRatio: Math.round(redundancyRatio * 100),
      accessPatternEfficiency: Math.round((1 - fragmentationLevel) * 100),
      compressionOpportunity: Math.round(compressionOpportunity * 100),
      cleanupPotential: Math.round(cleanupPotential * 100)
    };
  }

  private static generateActionableRecommendations(
    insights: DetailedInsight[],
    healthMetrics: StorageHealthMetrics
  ): string[] {
    const recommendations: string[] = [];

    // Priority recommendations based on insights
    const criticalInsights = insights.filter(i => i.severity === 'critical');
    if (criticalInsights.length > 0) {
      recommendations.push('🚨 URGENT: Address critical storage issues immediately to prevent device malfunction');
    }

    // Health-based recommendations
    if (healthMetrics.overallScore < 50) {
      recommendations.push('📊 Your storage health is poor. Consider a comprehensive cleanup strategy');
    }

    if (healthMetrics.compressionOpportunity > 30) {
      recommendations.push('🗜️ Significant compression opportunities available - could save 30%+ space');
    }

    if (healthMetrics.redundancyRatio > 20) {
      recommendations.push('🔄 High redundancy detected - removing duplicates could free substantial space');
    }

    if (healthMetrics.cleanupPotential > 25) {
      recommendations.push('🧹 Regular cleanup recommended - many old files can be safely removed');
    }

    // Actionable insights
    const actionableInsights = insights.filter(i => i.actionable && i.estimatedSavings);
    if (actionableInsights.length > 0) {
      const totalSavings = actionableInsights.reduce((sum, i) => sum + (i.estimatedSavings || 0), 0);
      recommendations.push(`💡 Quick actions available: Could save ${formatBytes(totalSavings)} with minimal effort`);
    }

    return recommendations;
  }
}
