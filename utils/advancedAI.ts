import * as FileSystem from 'expo-file-system';
import { FileInfo, StorageInfo, formatBytes } from './storageUtils';

export interface SmartCompressionResult {
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  qualityScore: number;
  method: string;
  timeToCompress: number;
}

export interface StoragePattern {
  type: 'seasonal' | 'usage-based' | 'app-specific' | 'media-heavy';
  description: string;
  recommendation: string;
  confidence: number;
}

export interface PredictiveInsight {
  timeframe: '1week' | '1month' | '3months';
  predictedUsage: number;
  storageFullDate?: Date;
  recommendation: string;
  confidence: number;
}

/**
 * Advanced AI-powered storage analysis engine
 */
export class AdvancedAIEngine {
  private static instance: AdvancedAIEngine;
  private userBehaviorData: any = {};
  private compressionHistory: SmartCompressionResult[] = [];

  static getInstance(): AdvancedAIEngine {
    if (!AdvancedAIEngine.instance) {
      AdvancedAIEngine.instance = new AdvancedAIEngine();
    }
    return AdvancedAIEngine.instance;
  }

  /**
   * Analyze user storage patterns using machine learning-like heuristics
   */
  async analyzeStoragePatterns(
    storageHistory: StorageInfo[],
    files: FileInfo[]
  ): Promise<StoragePattern[]> {
    const patterns: StoragePattern[] = [];

    // Analyze temporal patterns
    if (storageHistory.length >= 7) {
      const recentGrowth = this.calculateGrowthRate(storageHistory.slice(-7));
      const monthlyGrowth = this.calculateGrowthRate(storageHistory.slice(-30));

      if (recentGrowth > monthlyGrowth * 2) {
        patterns.push({
          type: 'seasonal',
          description: 'Rapid storage growth detected in recent days',
          recommendation: 'Consider enabling automatic cleanup or cloud backup',
          confidence: 85
        });
      }
    }

    // Analyze file type distribution
    const fileTypeDistribution = this.analyzeFileTypes(files);
    
    if (fileTypeDistribution.images > 0.6) {
      patterns.push({
        type: 'media-heavy',
        description: 'Your storage is dominated by photos and images',
        recommendation: 'Enable smart photo compression and cloud backup',
        confidence: 90
      });
    }

    if (fileTypeDistribution.videos > 0.4) {
      patterns.push({
        type: 'media-heavy',
        description: 'Videos are consuming significant storage space',
        recommendation: 'Consider video compression or streaming alternatives',
        confidence: 88
      });
    }

    // Analyze usage patterns
    const duplicateRatio = await this.calculateDuplicateRatio(files);
    if (duplicateRatio > 0.15) {
      patterns.push({
        type: 'usage-based',
        description: 'High number of duplicate files detected',
        recommendation: 'Regular duplicate cleanup could save significant space',
        confidence: 92
      });
    }

    return patterns;
  }

  /**
   * Predict future storage usage using trend analysis
   */
  async predictStorageUsage(
    storageHistory: StorageInfo[],
    currentFiles: FileInfo[]
  ): Promise<PredictiveInsight[]> {
    const insights: PredictiveInsight[] = [];

    if (storageHistory.length < 7) {
      return insights; // Need at least a week of data
    }

    const currentStorage = storageHistory[storageHistory.length - 1];
    const weeklyGrowth = this.calculateGrowthRate(storageHistory.slice(-7));
    const monthlyGrowth = this.calculateGrowthRate(storageHistory.slice(-30));

    // 1-week prediction
    const weekPrediction = currentStorage.usedSpace + (weeklyGrowth * 7);
    insights.push({
      timeframe: '1week',
      predictedUsage: weekPrediction,
      recommendation: weekPrediction > currentStorage.totalSpace * 0.9 
        ? 'Immediate cleanup recommended' 
        : 'Storage levels look stable',
      confidence: 75
    });

    // 1-month prediction
    const monthPrediction = currentStorage.usedSpace + (monthlyGrowth * 30);
    const monthInsight: PredictiveInsight = {
      timeframe: '1month',
      predictedUsage: monthPrediction,
      recommendation: 'Continue monitoring',
      confidence: 65
    };

    if (monthPrediction > currentStorage.totalSpace * 0.95) {
      monthInsight.storageFullDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
      monthInsight.recommendation = 'Plan for storage cleanup or upgrade';
    }

    insights.push(monthInsight);

    // 3-month prediction
    const quarterPrediction = currentStorage.usedSpace + (monthlyGrowth * 90);
    insights.push({
      timeframe: '3months',
      predictedUsage: quarterPrediction,
      storageFullDate: quarterPrediction > currentStorage.totalSpace 
        ? new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
        : undefined,
      recommendation: quarterPrediction > currentStorage.totalSpace 
        ? 'Consider storage upgrade or cloud migration'
        : 'Long-term storage outlook is stable',
      confidence: 50
    });

    return insights;
  }

  /**
   * Smart file categorization using AI-like heuristics
   */
  async categorizeFilesIntelligently(files: FileInfo[]): Promise<{
    [category: string]: {
      files: FileInfo[];
      totalSize: number;
      priority: 'high' | 'medium' | 'low';
      action: string;
    }
  }> {
    const categories: any = {};

    // Critical files (system, important docs)
    const criticalFiles = files.filter(f => 
      f.name.includes('important') || 
      f.name.includes('backup') ||
      f.type === 'document' && f.size > 1024 * 1024 // Large documents
    );

    if (criticalFiles.length > 0) {
      categories.critical = {
        files: criticalFiles,
        totalSize: criticalFiles.reduce((sum, f) => sum + f.size, 0),
        priority: 'high' as const,
        action: 'Backup to cloud, do not delete'
      };
    }

    // Compressible media
    const compressibleMedia = files.filter(f => 
      (f.type === 'image' || f.type === 'video') && 
      f.size > 5 * 1024 * 1024 // > 5MB
    );

    if (compressibleMedia.length > 0) {
      categories.compressible = {
        files: compressibleMedia,
        totalSize: compressibleMedia.reduce((sum, f) => sum + f.size, 0),
        priority: 'medium' as const,
        action: 'Compress without quality loss'
      };
    }

    // Junk files
    const junkFiles = files.filter(f => 
      f.name.includes('temp') || 
      f.name.includes('cache') ||
      f.name.includes('.tmp') ||
      f.type === 'other'
    );

    if (junkFiles.length > 0) {
      categories.junk = {
        files: junkFiles,
        totalSize: junkFiles.reduce((sum, f) => sum + f.size, 0),
        priority: 'low' as const,
        action: 'Safe to delete'
      };
    }

    return categories;
  }

  /**
   * Generate personalized optimization recommendations
   */
  async generatePersonalizedRecommendations(
    storageInfo: StorageInfo,
    files: FileInfo[],
    userPreferences: {
      keepPhotos: boolean;
      keepVideos: boolean;
      aggressiveCleanup: boolean;
    }
  ): Promise<string[]> {
    const recommendations: string[] = [];

    const usagePercentage = (storageInfo.usedSpace / storageInfo.totalSpace) * 100;
    const categorizedFiles = await this.categorizeFilesIntelligently(files);

    // Critical storage recommendations
    if (usagePercentage > 90) {
      recommendations.push('🚨 URGENT: Your storage is critically full. Immediate action required.');
      
      if (categorizedFiles.junk) {
        recommendations.push(`💡 Quick win: Delete ${formatBytes(categorizedFiles.junk.totalSize)} of junk files`);
      }
    }

    // Personalized based on preferences
    if (userPreferences.keepPhotos && categorizedFiles.compressible) {
      const imageFiles = categorizedFiles.compressible.files.filter(f => f.type === 'image');
      if (imageFiles.length > 0) {
        const imageSize = imageFiles.reduce((sum, f) => sum + f.size, 0);
        recommendations.push(`📸 Compress ${imageFiles.length} photos to save ~${formatBytes(imageSize * 0.4)} without losing quality`);
      }
    }

    if (!userPreferences.keepVideos && categorizedFiles.compressible) {
      const videoFiles = categorizedFiles.compressible.files.filter(f => f.type === 'video');
      if (videoFiles.length > 0) {
        recommendations.push(`🎥 Consider moving ${videoFiles.length} videos to cloud storage`);
      }
    }

    if (userPreferences.aggressiveCleanup) {
      const oldFiles = files.filter(f => {
        const age = Date.now() - (f.modificationTime || 0);
        return age > 180 * 24 * 60 * 60 * 1000; // 6 months
      });
      
      if (oldFiles.length > 0) {
        recommendations.push(`🧹 Found ${oldFiles.length} files older than 6 months that could be archived`);
      }
    }

    return recommendations;
  }

  // Helper methods
  private calculateGrowthRate(storageHistory: StorageInfo[]): number {
    if (storageHistory.length < 2) return 0;
    
    const recent = storageHistory[storageHistory.length - 1];
    const older = storageHistory[0];
    const days = storageHistory.length;
    
    return (recent.usedSpace - older.usedSpace) / days;
  }

  private analyzeFileTypes(files: FileInfo[]): { [type: string]: number } {
    const totalSize = files.reduce((sum, f) => sum + f.size, 0);
    const distribution: { [type: string]: number } = {};

    ['image', 'video', 'audio', 'document', 'other'].forEach(type => {
      const typeFiles = files.filter(f => f.type === type);
      const typeSize = typeFiles.reduce((sum, f) => sum + f.size, 0);
      distribution[type + 's'] = totalSize > 0 ? typeSize / totalSize : 0;
    });

    return distribution;
  }

  private async calculateDuplicateRatio(files: FileInfo[]): Promise<number> {
    // Simple duplicate detection based on size and name similarity
    const sizeGroups: { [size: number]: FileInfo[] } = {};
    
    files.forEach(file => {
      if (!sizeGroups[file.size]) {
        sizeGroups[file.size] = [];
      }
      sizeGroups[file.size].push(file);
    });

    let duplicateCount = 0;
    Object.values(sizeGroups).forEach(group => {
      if (group.length > 1) {
        duplicateCount += group.length - 1; // Count extras as duplicates
      }
    });

    return files.length > 0 ? duplicateCount / files.length : 0;
  }
}
